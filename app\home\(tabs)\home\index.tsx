import BellIcon from "../../../../assets/icons/Bellicon.svg";
import CoinIcon from "../../../../assets/Hometab/grommet-icons_money.svg";
import Icon from "../../../../assets/Hometab/rs.svg";
import OrdersReceiveComponent from "../../../../component/OrdersReceiveComponent/OrdersReceiveComponent";
import React, { useCallback, useState } from "react";
import ToggleSwitch from "toggle-switch-react-native";
import useSetApiData from "../../../../hooks/useSetApiData";
import useTenStackHook from "@/hooks/TenStackHook/TenStackHook";
import useTenStackMutate from "@/hooks/useTenStackMutate/useTenStackMutate";
import { router } from "expo-router";
import { FlatList, Image, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useSelector } from "react-redux";
import { UserStatus } from "../../../../store";
import { OrderListModel } from "@/data/model/order_model/order_list_model";
import { selectDashboard } from "@/redux/dashboard/meno";
import { selectProfile } from "@/redux/profile/meno";

const index = () => {
  const [Toggle, setToggle] = useState(false);
  const [activeTab, setactiveTab] = useState("All");
  const { setStatus } = UserStatus((state) => state);
  const [TabsData, setTabsData] = useState([
    {
      id: 1,
      name: "All",
    },
    {
      id: 2,
      name: "Pending",
    },
    {
      id: 3,
      name: "Accepted",
    },
    {
      id: 4,
      name: "Rejected",
    },
  ]);
  const { mutate: SetFunction } = useTenStackMutate({
    endpoint: "auth/delivery_person_online_offline",
    invalidateQueriesKey: ["orders"],
  });

  const { data: orders, isFetching } = useTenStackHook<
    {},
    { data: OrderListModel[] }
  >({
    key: "orders",
    canSave: true,
    endpoint: "order/all_order_list",
  });
  const { data } = useTenStackHook<{}, { data: UserEntity }>({
    key: "getProfile",
    canSave: true,
    endpoint: "/getProfile",
  });
  const ListCompopnentHeader = useCallback(() => {
    const profile = useSelector(selectProfile);
    const dashboard = useSelector(selectDashboard);
    return (
      <>
        <View className="mt-4 px-4 flex-row justify-between items-center">
          <View className="flex-row items-center space-x-6">
            {profile.image ? (
              <>
                <Image
                  source={{ uri: profile.image }}
                  className="w-[50px] h-[50px] rounded-full"
                />
              </>
            ) : (
              <View className="w-[50px] h-[50px] rounded-full bg-[#00660A]"></View>
            )}
            <Text className="font-[500] text-[20px] leading-[30px] font-Pop">
              {profile.name}
            </Text>
          </View>
          <TouchableOpacity
            onPress={() => {
              router.push("/home/<USER>/home/<USER>");
            }}
          >
            <BellIcon />
          </TouchableOpacity>
        </View>
        <View className="items-end mx-4">
          <ToggleSwitch
            isOn={Toggle}
            onColor="#28C979"
            offColor="#D13434"
            size="medium"
            onToggle={(val) => {
              setToggle(val);
              setStatus(val);
              SetFunction({
                is_online: val ? 1 : 0,
              });
            }}
          />
          <Text
            className="font-[500] text-[14px] leading-[16px] mt-1"
            style={{
              color: Toggle ? "#28C979" : "#D13434",
            }}
          >
            {Toggle ? "On Duty" : "Off Duty"}
          </Text>
        </View>

        <View className="mt-4 px-4">
          <View className="">
            <TouchableOpacity
              onPress={() => {
                router.push("/home/<USER>/profile/yourearnings");
              }}
              className="flex-row items-center justify-center space-x-2 h-[49px] bg-[#D4FFD8]"
            >
              <CoinIcon />
              <Text className="text-[#00660A] font-[500] leading-[27px] font-Pop text-[18px]">
                Earnings
              </Text>
            </TouchableOpacity>
            <View className="mt-4 p-4 flex-row">
              <View className="flex-1 items-center justify-center">
                <View className="flex-row items-center space-x-2">
                  <Icon />
                  <Text className="font-[600] text-[24px] leading-[36px] font-Pop text-[#001A03]">
                    {dashboard.weekly_earned}
                  </Text>
                </View>
                <Text className="font-[500] text-[12px] font-Pop text-[#00660A] leading-[18px]">
                  This Week
                </Text>
              </View>
              <View className="w-[1px] h-full bg-[#939D94]" />
              <View className="flex-1 items-center justify-center">
                <View className="flex-row items-center space-x-2">
                  <Icon />
                  <Text className="font-[600] text-[24px] leading-[36px] font-Pop text-[#001A03]">
                    {dashboard.past_earned}
                  </Text>
                </View>
                <Text className="font-[500] text-[12px] font-Pop text-[#00660A] leading-[18px]">
                  Past Earnings
                </Text>
              </View>
              <View className="w-[1px] h-full bg-[#939D94]" />
              <View className="flex-1 items-center justify-center">
                <Text className="font-[600] text-[24px] leading-[36px] font-Pop text-[#001A03]">
                  {dashboard.distance_traveled} kms
                </Text>
                <Text className="font-[500] text-[12px] font-Pop text-[#00660A] leading-[18px]">
                  Distance travelled
                </Text>
              </View>
            </View>
          </View>
        </View>

        {Toggle ? (
          <>
            <View className="px-4">
              <View className="mb-4">
                <Text className="font-[500] text-[16px] leading-[20px] font-Pop">
                  My Orders
                </Text>
              </View>
              <View
                className="flex-row items-center space-x-4 "
                style={{
                  overflow: "scroll",
                }}
              >
                <Tabs
                  TabsData={TabsData}
                  activeTab={activeTab}
                  setactiveTab={setactiveTab}
                />
              </View>
            </View>
          </>
        ) : (
          <>
            <View className="mt-10 items-center justify-center">
              <TouchableOpacity
                onPress={() => {
                  setToggle(true);
                  setStatus(true);
                }}
                className="h-[48px] items-center justify-center w-[278px] bg-[#00660A] rounded-[8px]"
              >
                <Text className="font-[500] font-Pop text-[16px] leading-[24px] text-[#fff]">
                  Start Duty
                </Text>
              </TouchableOpacity>
            </View>
          </>
        )}
      </>
    );
  }, [Toggle]);

  const getList = (order: OrderListModel[]) => {
    const list =
      order?.filter(
        (item) =>
          String(item?.deliver_partner_id) === data?.data?.id.toString().trim()
      ) ?? [];
    if (list.length === 0) {
      return [
        order?.find((item) => {
          if (item?.deliver_partner_id === "") {
            return item;
          }
        }),
      ];
    }
    return list;
  };
  return (
    <SafeAreaView>
      <FlatList
        ListHeaderComponent={ListCompopnentHeader}
        keyExtractor={(item) => {
          return item?.id?.toString() ?? Math.random().toString();
        }}
        showsVerticalScrollIndicator={false}
        data={isFetching ? [] : getList(orders?.data ?? [])}
        renderItem={({ item }) => {
          if (!item) return null;
          if (!Toggle) return null;

          if (activeTab === "All" || activeTab === item?.order_status) {
            return (
              <View className="my-4 px-4" key={item?.id}>
                <OrdersReceiveComponent
                  key={item?.id}
                  id={item?.id ?? ""}
                  mode={item?.product_mode == 1 ? "COD" : "Online"}
                  invoice_no={item?.invoice_no ?? ""}
                  statusstate={
                    item?.deliver_partner_id === ""
                      ? "Pending"
                      : String(item?.deliver_partner_id) ===
                        data?.data?.id.toString().trim()
                      ? "Accepted"
                      : "Rejected"
                  }
                  orderNumber={item?.order_number ?? ""}
                  PickUpAddress={item?.pickup_address ?? ""}
                  DeliveryAddress={item?.delivery_address ?? ""}
                  edt={item?.edt ?? ""}
                  delivery_fees={item?.delivery_fees ?? 0}
                />
              </View>
            );
          }
          return null;
        }}
        ListEmptyComponent={() => (
          <View className="flex-1 items-center justify-center">
            <Text>No orders available</Text>
          </View>
        )}
      />
    </SafeAreaView>
  );
};

const Tabs = ({
  TabsData,
  activeTab,
  setactiveTab,
}: {
  TabsData: any;
  activeTab: any;
  setactiveTab: any;
}) => {
  const [active, setactive] = useState(activeTab);
  return (
    <FlatList
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={{
        gap: 20,
      }}
      data={TabsData}
      renderItem={({ item: { id, name } }) => {
        return (
          <TouchableOpacity
            onPress={() => {
              setactiveTab(name);
              setactive(name);
            }}
            className="px-8 border-[1px] border-[#ACB9D5] rounded-[5px] py-2 bg-[#5800E5]"
            style={
              active === name
                ? { backgroundColor: "#5800E5" }
                : { backgroundColor: "#fff" }
            }
          >
            <View className="flex-row space-x-2">
              <Text
                className="font-[400] text-[14px] leading-[18px] font-Pop"
                style={
                  active === name ? { color: "#fff" } : { color: "#310080" }
                }
              >
                {name}
              </Text>
            </View>
          </TouchableOpacity>
        );
      }}
    />
  );
};
export default index;
